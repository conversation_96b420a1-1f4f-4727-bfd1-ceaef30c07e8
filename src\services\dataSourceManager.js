const ReaLabService = require('./realabService');
const HuiHiFiService = require('./huihifiService');

class DataSourceManager {
    constructor() {
        this.dataSources = new Map();
        this.initializeDataSources();
    }

    /**
     * 初始化所有数据源
     */
    initializeDataSources() {
        // 添加ReaLab数据源
        const realabService = new ReaLabService();
        this.dataSources.set('realab', {
            service: realabService,
            name: 'realab',
            displayName: 'ReaLab',
            description: 'ReaLab 阅真实验室 - 专业/现代/5128'
        });

        // 添加HuiHiFi数据源
        const huihifiService = new HuiHiFiService();
        this.dataSources.set('huihifi', {
            service: huihifiService,
            name: 'huihifi',
            displayName: 'HuiHiFi',
            description: '毁HiFi - 全面/详尽/4128'
        });
    }

    /**
     * 获取所有数据源信息
     * @returns {Array} 数据源列表
     */
    getAllSources() {
        const sources = [];
        for (const [key, source] of this.dataSources) {
            sources.push({
                name: source.name,
                displayName: source.displayName,
                description: source.description
            });
        }
        return sources;
    }

    /**
     * 获取指定数据源的服务实例
     * @param {string} sourceName 数据源名称
     * @returns {Object|null} 数据源服务实例
     */
    getDataSource(sourceName) {
        const source = this.dataSources.get(sourceName);
        return source ? source.service : null;
    }

    /**
     * 检查数据源是否存在
     * @param {string} sourceName 数据源名称
     * @returns {boolean} 是否存在
     */
    hasDataSource(sourceName) {
        return this.dataSources.has(sourceName);
    }

    /**
     * 获取数据源显示名称
     * @param {string} sourceName 数据源名称
     * @returns {string} 显示名称
     */
    getSourceDisplayName(sourceName) {
        const source = this.dataSources.get(sourceName);
        return source ? source.displayName : sourceName;
    }

    /**
     * 获取所有数据源的品牌数据
     * @returns {Promise<Object>} 所有数据源的品牌数据
     */
    async getAllBrandsFromAllSources() {
        const allSourcesData = {};

        for (const [sourceName, source] of this.dataSources) {
            try {
                console.log(`正在获取${source.displayName}的品牌数据...`);
                const brands = await source.service.getBrands();
                allSourcesData[sourceName] = brands;
                console.log(`${source.displayName}获取到${brands.length}个品牌`);
            } catch (error) {
                console.error(`获取${source.displayName}品牌数据失败:`, error.message);
                allSourcesData[sourceName] = [];
            }
        }

        return allSourcesData;
    }

    /**
     * 获取指定数据源的所有数据
     * @param {string} sourceName 数据源名称
     * @returns {Promise<Object>} 数据源的所有数据
     */
    async getAllDataFromSource(sourceName) {
        const source = this.getDataSource(sourceName);
        if (!source) {
            throw new Error(`数据源 ${sourceName} 不存在`);
        }

        try {
            console.log(`开始获取${this.getSourceDisplayName(sourceName)}的所有数据...`);
            const data = await source.getAllData();
            console.log(`${this.getSourceDisplayName(sourceName)}数据获取完成`);
            return data;
        } catch (error) {
            console.error(`获取${this.getSourceDisplayName(sourceName)}数据失败:`, error.message);
            throw error;
        }
    }

    /**
     * 并行获取所有数据源的数据
     * @returns {Promise<Object>} 所有数据源的数据
     */
    async getAllDataFromAllSources() {
        const allSourcesData = {};
        const promises = [];

        for (const [sourceName, source] of this.dataSources) {
            const promise = this.getAllDataFromSource(sourceName)
                .then(data => {
                    allSourcesData[sourceName] = data;
                })
                .catch(error => {
                    console.error(`获取${source.displayName}数据失败:`, error.message);
                    allSourcesData[sourceName] = {};
                });
            promises.push(promise);
        }

        await Promise.all(promises);
        return allSourcesData;
    }

    /**
     * 获取指定数据源和品牌的产品列表
     * @param {string} sourceName 数据源名称
     * @param {string} brandId 品牌ID或UUID
     * @returns {Promise<Array>} 产品列表
     */
    async getProductsByBrand(sourceName, brandId) {
        const source = this.getDataSource(sourceName);
        if (!source) {
            throw new Error(`数据源 ${sourceName} 不存在`);
        }

        try {
            if (sourceName === 'realab') {
                return await source.getProductsByBrand(brandId);
            } else if (sourceName === 'huihifi') {
                return await source.getProductsByBrand(brandId);
            }
            throw new Error(`不支持的数据源: ${sourceName}`);
        } catch (error) {
            console.error(`获取${sourceName}品牌${brandId}的产品失败:`, error.message);
            throw error;
        }
    }

    /**
     * 获取指定数据源和产品的频响数据
     * @param {string} sourceName 数据源名称
     * @param {string} productId 产品ID或UUID
     * @returns {Promise<Object>} 频响数据
     */
    async getFrequencyResponse(sourceName, productId) {
        const source = this.getDataSource(sourceName);
        if (!source) {
            throw new Error(`数据源 ${sourceName} 不存在`);
        }

        try {
            if (sourceName === 'realab') {
                return await source.getFrequencyResponse(productId);
            } else if (sourceName === 'huihifi') {
                const headphoneDetail = await source.getFrequencyResponse(productId);
                return source.extractFrequencyData(headphoneDetail);
            }
            throw new Error(`不支持的数据源: ${sourceName}`);
        } catch (error) {
            console.error(`获取${sourceName}产品${productId}的频响数据失败:`, error.message);
            throw error;
        }
    }
}

module.exports = DataSourceManager;
