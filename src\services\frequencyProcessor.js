class FrequencyProcessor {
    constructor() {
        // 标准AutoEq频率点 (Hz)
        this.STANDARD_FREQUENCIES = [
            20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 34, 36, 38, 40, 43, 45, 48, 50,
            53, 56, 59, 63, 66, 70, 74, 78, 83, 87, 92, 97, 103, 109, 115, 121, 128,
            136, 143, 151, 160, 169, 178, 188, 199, 210, 222, 235, 248, 262, 277, 292,
            309, 326, 345, 364, 385, 406, 429, 453, 479, 506, 534, 565, 596, 630, 665,
            703, 743, 784, 829, 875, 924, 977, 1032, 1090, 1151, 1216, 1284, 1357, 1433,
            1514, 1599, 1689, 1784, 1885, 1991, 2103, 2221, 2347, 2479, 2618, 2766, 2921,
            3086, 3260, 3443, 3637, 3842, 4058, 4287, 4528, 4783, 5052, 5337, 5637, 5955,
            6290, 6644, 7018, 7414, 7831, 8272, 8738, 9230, 9749, 10298, 10878, 11490,
            12137, 12821, 13543, 14305, 15110, 15961, 16860, 17809, 18812, 19871
        ];
    }

    /**
     * 处理原始频响数据，转换为标准频率点
     * @param {Array} rawFrequencyData 原始频响数据数组
     * @returns {Object} 处理后的频响数据
     */
    processFrequencyData(rawFrequencyData) {
        const processedData = [];

        for (const measurement of rawFrequencyData) {
            if (!measurement.data || !Array.isArray(measurement.data)) {
                continue;
            }

            // 跳过标题行
            const dataPoints = measurement.data.slice(1);
            
            // 解析原始数据点
            const originalPoints = dataPoints.map(point => {
                const freq = parseFloat(point[0]);
                const spl = parseFloat(point[1]);
                return { frequency: freq, spl: spl };
            }).filter(point => !isNaN(point.frequency) && !isNaN(point.spl));

            // 按频率排序
            originalPoints.sort((a, b) => a.frequency - b.frequency);

            // 转换为标准频率点
            const standardPoints = this.interpolateToStandardFrequencies(originalPoints);

            processedData.push({
                title: measurement.title,
                data: standardPoints
            });
        }

        return processedData;
    }

    /**
     * 将原始数据插值到标准频率点
     * @param {Array} originalPoints 原始数据点
     * @returns {Array} 标准频率点数据
     */
    interpolateToStandardFrequencies(originalPoints) {
        const result = [];

        for (const targetFreq of this.STANDARD_FREQUENCIES) {
            const spl = this.interpolateValue(originalPoints, targetFreq);
            result.push({
                frequency: targetFreq,
                spl: spl
            });
        }

        return result;
    }

    /**
     * 为指定频率插值SPL值
     * @param {Array} points 原始数据点
     * @param {number} targetFreq 目标频率
     * @returns {number} 插值后的SPL值
     */
    interpolateValue(points, targetFreq) {
        if (points.length === 0) {
            return 0;
        }

        // 查找最接近的点
        let closestPoint = points[0];
        let minDistance = Math.abs(points[0].frequency - targetFreq);

        for (const point of points) {
            const distance = Math.abs(point.frequency - targetFreq);
            if (distance < minDistance) {
                minDistance = distance;
                closestPoint = point;
            }
        }

        // 如果找到完全匹配的频率点
        if (minDistance === 0) {
            return closestPoint.spl;
        }

        // 尝试线性插值
        let lowerPoint = null;
        let upperPoint = null;

        for (let i = 0; i < points.length - 1; i++) {
            if (points[i].frequency <= targetFreq && points[i + 1].frequency >= targetFreq) {
                lowerPoint = points[i];
                upperPoint = points[i + 1];
                break;
            }
        }

        // 如果可以进行线性插值
        if (lowerPoint && upperPoint && lowerPoint.frequency !== upperPoint.frequency) {
            const ratio = (targetFreq - lowerPoint.frequency) / (upperPoint.frequency - lowerPoint.frequency);
            return lowerPoint.spl + ratio * (upperPoint.spl - lowerPoint.spl);
        }

        // 否则返回最接近点的值
        return closestPoint.spl;
    }

    /**
     * 简化频响数据结构，只保留必要信息
     * @param {Array} processedData 处理后的频响数据
     * @returns {Object} 简化后的数据结构
     */
    simplifyData(processedData) {
        const simplified = {};

        for (const measurement of processedData) {
            // 提取测量条件信息
            const measurementName = this.extractMeasurementName(measurement.title);
            
            simplified[measurementName] = {
                title: measurement.title,
                frequencies: this.STANDARD_FREQUENCIES,
                spl_values: measurement.data.map(point => point.spl)
            };
        }

        return simplified;
    }

    /**
     * 从测量标题中提取简化的测量名称
     * @param {string} title 完整标题
     * @returns {string} 简化的测量名称
     */
    extractMeasurementName(title) {
        // 移除品牌和型号信息，保留测量条件
        const parts = title.split(' - ');
        if (parts.length > 2) {
            // 对于HuiHiFi格式：耳机名 - 调音模式 - 测量条件
            return `${parts[1].trim()} - ${parts[2].trim()}`;
        } else if (parts.length > 1) {
            // 对于ReaLab格式：耳机名 - 测量条件
            return parts[1].trim();
        }
        return title;
    }
}

module.exports = FrequencyProcessor;
