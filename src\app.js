const express = require('express');
const cors = require('cors');
const path = require('path');
const DataSyncService = require('./services/dataSync');
const { router: apiRouter, setDataSyncService } = require('./routes/api');

class HeadphoneFrequencyAPI {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.dataSyncService = new DataSyncService();
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
        this.initializeServices();
    }

    /**
     * 设置中间件
     */
    setupMiddleware() {
        // CORS配置
        this.app.use(cors({
            origin: process.env.CORS_ORIGIN || '*',
            methods: ['GET', 'POST', 'DELETE'],
            allowedHeaders: ['Content-Type', 'Authorization']
        }));

        // 解析JSON请求体
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });

        // 静态文件服务（如果需要）
        this.app.use('/static', express.static(path.join(__dirname, '../public')));
    }

    /**
     * 设置路由
     */
    setupRoutes() {
        // 设置数据同步服务实例
        setDataSyncService(this.dataSyncService);

        // API路由
        this.app.use('/api', apiRouter);

        // 根路径
        this.app.get('/', (req, res) => {
            res.json({
                name: 'Headphone Frequency Response API',
                version: '2.0.0',
                description: 'API for fetching and caching headphone frequency response data from multiple sources (ReaLab, HuiHiFi)',
                dataSources: ['realab', 'huihifi'],
                endpoints: {
                    sources: 'GET /api/sources',
                    brands: 'GET /api/brands (legacy)',
                    sourceBrands: 'GET /api/sources/:sourceName/brands',
                    headphones: 'GET /api/brands/:brandName/headphones (legacy)',
                    sourceHeadphones: 'GET /api/sources/:sourceName/brands/:brandName/headphones',
                    frequencyData: 'GET /api/brands/:brandName/headphones/:headphoneName (legacy)',
                    sourceFrequencyData: 'GET /api/sources/:sourceName/brands/:brandName/headphones/:headphoneName',
                    targets: 'GET /api/targets',
                    targetData: 'GET /api/targets/:targetName',
                    status: 'GET /api/status',
                    health: 'GET /api/health',
                    update: 'POST /api/update',
                    clearCache: 'DELETE /api/cache'
                },
                documentation: {
                    swagger: '/api/docs',
                    github: 'https://github.com/your-repo/headphone-fr-api'
                }
            });
        });

        // 404处理
        this.app.use('*', (req, res) => {
            res.status(404).json({
                success: false,
                error: 'API端点不存在',
                path: req.originalUrl,
                availableEndpoints: [
                    'GET /api/sources',
                    'GET /api/sources/:sourceName/brands',
                    'GET /api/sources/:sourceName/brands/:brandName/headphones',
                    'GET /api/sources/:sourceName/brands/:brandName/headphones/:headphoneName',
                    'GET /api/targets',
                    'GET /api/targets/:targetName',
                    'GET /api/brands (legacy)',
                    'GET /api/brands/:brandName/headphones (legacy)',
                    'GET /api/brands/:brandName/headphones/:headphoneName (legacy)',
                    'GET /api/status',
                    'GET /api/health',
                    'POST /api/update',
                    'DELETE /api/cache'
                ]
            });
        });
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误处理中间件
        this.app.use((error, req, res, next) => {
            console.error('全局错误处理:', error);

            // 如果响应已经发送，交给默认错误处理器
            if (res.headersSent) {
                return next(error);
            }

            // 根据错误类型返回不同的状态码
            let statusCode = 500;
            let message = '服务器内部错误';

            if (error.name === 'ValidationError') {
                statusCode = 400;
                message = '请求参数验证失败';
            } else if (error.name === 'UnauthorizedError') {
                statusCode = 401;
                message = '未授权访问';
            } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                statusCode = 503;
                message = '外部服务不可用';
            }

            res.status(statusCode).json({
                success: false,
                error: message,
                details: process.env.NODE_ENV === 'development' ? error.message : undefined,
                timestamp: new Date().toISOString()
            });
        });

        // 处理未捕获的异常
        process.on('uncaughtException', (error) => {
            console.error('未捕获的异常:', error);
            // 在生产环境中，可能需要重启应用
            if (process.env.NODE_ENV === 'production') {
                process.exit(1);
            }
        });

        // 处理未处理的Promise拒绝
        process.on('unhandledRejection', (reason, promise) => {
            console.error('未处理的Promise拒绝:', reason);
            // 在生产环境中，可能需要重启应用
            if (process.env.NODE_ENV === 'production') {
                process.exit(1);
            }
        });
    }

    /**
     * 初始化服务
     */
    async initializeServices() {
        try {
            console.log('正在初始化数据同步服务...');
            
            // 启动定时更新任务
            this.dataSyncService.startScheduledUpdates();
            
            console.log('数据同步服务初始化完成');
        } catch (error) {
            console.error('服务初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 启动服务器
     */
    async start() {
        try {
            const server = this.app.listen(this.port, () => {
                console.log('\n=================================');
                console.log('🎧 Headphone Frequency Response API');
                console.log('=================================');
                console.log(`🚀 服务器运行在: http://localhost:${this.port}`);
                console.log(`📊 API文档: http://localhost:${this.port}/api`);
                console.log(`💚 健康检查: http://localhost:${this.port}/api/health`);
                console.log(`📈 系统状态: http://localhost:${this.port}/api/status`);
                console.log('=================================\n');
            });

            // 优雅关闭处理
            const gracefulShutdown = () => {
                console.log('\n正在关闭服务器...');
                
                server.close(() => {
                    console.log('HTTP服务器已关闭');
                    
                    // 停止定时任务
                    if (this.dataSyncService) {
                        this.dataSyncService.stopScheduledUpdates();
                    }
                    
                    console.log('服务已完全关闭');
                    process.exit(0);
                });

                // 强制关闭超时
                setTimeout(() => {
                    console.error('强制关闭服务器');
                    process.exit(1);
                }, 10000);
            };

            // 监听关闭信号
            process.on('SIGTERM', gracefulShutdown);
            process.on('SIGINT', gracefulShutdown);

            return server;
        } catch (error) {
            console.error('启动服务器失败:', error.message);
            throw error;
        }
    }
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
    const api = new HeadphoneFrequencyAPI();
    api.start().catch(error => {
        console.error('应用启动失败:', error.message);
        process.exit(1);
    });
}

module.exports = HeadphoneFrequencyAPI;
