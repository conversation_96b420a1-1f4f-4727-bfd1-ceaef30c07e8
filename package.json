{"name": "headphone-frequency-response-api", "version": "1.0.0", "description": "API for fetching and caching headphone frequency response data from ReaLab", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["headphone", "frequency-response", "api", "realab", "audio"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "node-cron": "^3.0.3", "fs-extra": "^11.1.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}