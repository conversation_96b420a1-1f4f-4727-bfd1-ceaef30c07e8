const axios = require('axios');

class HuiHiFiService {
    constructor() {
        this.baseUrl = 'https://huihifi.com/api/v1';
        this.axiosInstance = axios.create({
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Content-Type': 'application/json'
            }
        });
    }

    /**
     * 获取所有品牌
     * @returns {Promise<Array>} 品牌列表
     */
    async getBrands() {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/brands/all`);
            if (response.data.code === 0) {
                return response.data.data;
            }
            throw new Error(`API返回错误: ${response.data.message}`);
        } catch (error) {
            console.error('获取HuiHiFi品牌失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取指定品牌下的所有耳机型号
     * @param {string} brandUuid 品牌UUID
     * @returns {Promise<Array>} 耳机型号列表
     */
    async getProductsByBrand(brandUuid) {
        try {
            const requestData = {
                brandUuids: [brandUuid],
                pageSize: 999,
                direction: "DESC",
                orderBy: "article.publishTime"
            };

            const response = await this.axiosInstance.post(`${this.baseUrl}/evaluations`, requestData);
            if (response.data.code === 0) {
                return response.data.data.list || [];
            }
            throw new Error(`API返回错误: ${response.data.message}`);
        } catch (error) {
            console.error(`获取HuiHiFi品牌${brandUuid}的产品失败:`, error.message);
            throw error;
        }
    }

    /**
     * 获取指定耳机的频响数据
     * @param {string} headphoneUuid 耳机的UUID标识
     * @returns {Promise<Object>} 频响数据
     */
    async getFrequencyResponse(headphoneUuid) {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/evaluations/${headphoneUuid}`);
            if (response.data.code === 0) {
                return response.data.data;
            }
            throw new Error(`API返回错误: ${response.data.message}`);
        } catch (error) {
            console.error(`获取HuiHiFi耳机${headphoneUuid}的频响数据失败:`, error.message);
            throw error;
        }
    }

    /**
     * 从耳机详情数据中提取频响数据
     * @param {Object} headphoneDetail 耳机详情数据
     * @returns {Array} 频响数据数组
     */
    extractFrequencyData(headphoneDetail) {
        const frequencyDataList = [];

        if (!headphoneDetail.datas || !Array.isArray(headphoneDetail.datas)) {
            return frequencyDataList;
        }

        for (const dataItem of headphoneDetail.datas) {
            // 只处理频响曲线数据
            if (dataItem.project && dataItem.project.name === '频响曲线 Frequency Response') {
                if (dataItem.children && Array.isArray(dataItem.children)) {
                    for (const child of dataItem.children) {
                        if (child.dataSet && Array.isArray(child.dataSet)) {
                            frequencyDataList.push({
                                title: `${headphoneDetail.title} - ${child.name}`,
                                group: dataItem.group,
                                name: child.name,
                                data: child.dataSet
                            });
                        }
                    }
                }
            }
        }

        return frequencyDataList;
    }

    /**
     * 获取所有品牌的所有耳机数据
     * @returns {Promise<Object>} 完整的数据结构
     */
    async getAllData() {
        try {
            console.log('开始获取HuiHiFi所有品牌...');
            const brands = await this.getBrands();
            console.log(`获取到${brands.length}个HuiHiFi品牌`);

            const allData = {};

            for (const brand of brands) {
                console.log(`正在处理HuiHiFi品牌: ${brand.title}`);
                try {
                    const products = await this.getProductsByBrand(brand.uuid);
                    console.log(`HuiHiFi品牌${brand.title}有${products.length}个产品`);

                    allData[brand.title] = {
                        brandUuid: brand.uuid,
                        products: {}
                    };

                    for (const product of products) {
                        console.log(`正在获取HuiHiFi产品: ${product.title}`);
                        try {
                            const headphoneDetail = await this.getFrequencyResponse(product.uuid);
                            const frequencyData = this.extractFrequencyData(headphoneDetail);
                            
                            if (frequencyData.length > 0) {
                                allData[brand.title].products[product.title] = {
                                    uuid: product.uuid,
                                    category: product.category,
                                    price: product.price,
                                    frequencyData: frequencyData
                                };
                            } else {
                                console.log(`产品${product.title}没有频响数据`);
                            }
                            
                            // 添加延迟避免请求过于频繁
                            await this.delay(200);
                        } catch (error) {
                            console.error(`获取HuiHiFi产品${product.title}的频响数据失败:`, error.message);
                            // 继续处理其他产品
                        }
                    }
                    
                    // 品牌间添加更长延迟
                    await this.delay(1000);
                } catch (error) {
                    console.error(`处理HuiHiFi品牌${brand.title}失败:`, error.message);
                    // 继续处理其他品牌
                }
            }

            return allData;
        } catch (error) {
            console.error('获取HuiHiFi所有数据失败:', error.message);
            throw error;
        }
    }

    /**
     * 延迟函数
     * @param {number} ms 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取数据源名称
     * @returns {string} 数据源名称
     */
    getSourceName() {
        return 'huihifi';
    }

    /**
     * 获取数据源显示名称
     * @returns {string} 数据源显示名称
     */
    getSourceDisplayName() {
        return 'HuiHiFi';
    }
}

module.exports = HuiHiFiService;
