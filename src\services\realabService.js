const axios = require('axios');

class ReaLabService {
    constructor() {
        this.baseUrl = 'https://www.realab.com/nice-json/front-end';
        this.axiosInstance = axios.create({
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });
    }

    /**
     * 获取所有品牌
     * @returns {Promise<Array>} 品牌列表
     */
    async getBrands() {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/brand`);
            if (response.data.code === 200) {
                return response.data.data;
            }
            throw new Error(`API返回错误: ${response.data.message}`);
        } catch (error) {
            console.error('获取品牌失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取指定品牌下的所有耳机型号
     * @param {number} brandId 品牌ID
     * @returns {Promise<Array>} 耳机型号列表
     */
    async getProductsByBrand(brandId) {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/product?brand=${brandId}`);
            if (response.data.code === 200) {
                return response.data.data;
            }
            throw new Error(`API返回错误: ${response.data.message}`);
        } catch (error) {
            console.error(`获取品牌${brandId}的产品失败:`, error.message);
            throw error;
        }
    }

    /**
     * 获取指定耳机的频响数据
     * @param {string} slug 耳机的slug标识
     * @returns {Promise<Array>} 频响数据
     */
    async getFrequencyResponse(slug) {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/data?slug=${slug}`);
            if (response.data.code === 200) {
                return response.data.data;
            }
            throw new Error(`API返回错误: ${response.data.message}`);
        } catch (error) {
            console.error(`获取耳机${slug}的频响数据失败:`, error.message);
            throw error;
        }
    }

    /**
     * 获取所有品牌的所有耳机数据
     * @returns {Promise<Object>} 完整的数据结构
     */
    async getAllData() {
        try {
            console.log('开始获取所有品牌...');
            const brands = await this.getBrands();
            console.log(`获取到${brands.length}个品牌`);

            const allData = {};

            for (const brand of brands) {
                console.log(`正在处理品牌: ${brand.name}`);
                try {
                    const products = await this.getProductsByBrand(brand.id);
                    console.log(`品牌${brand.name}有${products.length}个产品`);

                    allData[brand.name] = {
                        brandId: brand.id,
                        products: {}
                    };

                    for (const product of products) {
                        console.log(`正在获取产品: ${product.title}`);
                        try {
                            const frequencyData = await this.getFrequencyResponse(product.slug);
                            allData[brand.name].products[product.title] = {
                                slug: product.slug,
                                frequencyData: frequencyData
                            };
                            
                            // 添加延迟避免请求过于频繁
                            await this.delay(100);
                        } catch (error) {
                            console.error(`获取产品${product.title}的频响数据失败:`, error.message);
                            // 继续处理其他产品
                        }
                    }
                    
                    // 品牌间添加更长延迟
                    await this.delay(500);
                } catch (error) {
                    console.error(`处理品牌${brand.name}失败:`, error.message);
                    // 继续处理其他品牌
                }
            }

            return allData;
        } catch (error) {
            console.error('获取所有数据失败:', error.message);
            throw error;
        }
    }

    /**
     * 延迟函数
     * @param {number} ms 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ReaLabService;
