const axios = require('axios');
const fs = require('fs-extra');
const path = require('path');

class ReaLabTargetParser {
    constructor() {
        this.targetDir = path.join(process.cwd(), 'data', 'target');
        this.realabTargetUrl = 'https://www.realab.com/nice-json/front-end/target-data?type=1';
        
        // 标准AutoEq频率点 (Hz)
        this.STANDARD_FREQUENCIES = [
            20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 34, 36, 38, 40, 43, 45, 48, 50,
            53, 56, 59, 63, 66, 70, 74, 78, 83, 87, 92, 97, 103, 109, 115, 121, 128,
            136, 143, 151, 160, 169, 178, 188, 199, 210, 222, 235, 248, 262, 277, 292,
            309, 326, 345, 364, 385, 406, 429, 453, 479, 506, 534, 565, 596, 630, 665,
            703, 743, 784, 829, 875, 924, 977, 1032, 1090, 1151, 1216, 1284, 1357, 1433,
            1514, 1599, 1689, 1784, 1885, 1991, 2103, 2221, 2347, 2479, 2618, 2766, 2921,
            3086, 3260, 3443, 3637, 3842, 4058, 4287, 4528, 4783, 5052, 5337, 5637, 5955,
            6290, 6644, 7018, 7414, 7831, 8272, 8738, 9230, 9749, 10298, 10878, 11490,
            12137, 12821, 13543, 14305, 15110, 15961, 16860, 17809, 18812, 19871
        ];
    }

    /**
     * 获取ReaLab目标曲线数据
     */
    async fetchReaLabTargets() {
        try {
            console.log('🎯 正在获取ReaLab目标曲线数据...');
            
            const response = await axios.get(this.realabTargetUrl, {
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
            });

            if (response.data.code !== 200) {
                throw new Error(`API返回错误: ${response.data.message}`);
            }

            return response.data.data;
        } catch (error) {
            console.error('获取ReaLab目标曲线失败:', error.message);
            throw error;
        }
    }

    /**
     * 解析目标曲线数据
     * @param {Object} targetData 目标曲线数据
     * @returns {Object} 解析后的数据
     */
    parseTargetData(targetData) {
        const dataPoints = [];
        
        // 跳过标题行，从第二行开始解析数据
        for (let i = 1; i < targetData.data.length; i++) {
            const row = targetData.data[i];
            if (row.length >= 2) {
                const frequency = parseFloat(row[0]);
                const spl = parseFloat(row[1]);
                
                if (!isNaN(frequency) && !isNaN(spl)) {
                    dataPoints.push({ frequency, spl });
                }
            }
        }

        return {
            name: targetData.name,
            id: targetData.id,
            dataPoints: dataPoints
        };
    }

    /**
     * 将数据点插值到标准频率点
     * @param {Array} originalPoints 原始数据点
     * @returns {Array} 标准频率点数据
     */
    interpolateToStandardFrequencies(originalPoints) {
        const result = [];

        // 按频率排序
        originalPoints.sort((a, b) => a.frequency - b.frequency);

        for (const targetFreq of this.STANDARD_FREQUENCIES) {
            const spl = this.interpolateValue(originalPoints, targetFreq);
            result.push({
                frequency: targetFreq,
                spl: spl
            });
        }

        return result;
    }

    /**
     * 为指定频率插值SPL值
     * @param {Array} points 原始数据点
     * @param {number} targetFreq 目标频率
     * @returns {number} 插值后的SPL值
     */
    interpolateValue(points, targetFreq) {
        if (points.length === 0) {
            return 0;
        }

        // 查找最接近的点
        let closestPoint = points[0];
        let minDistance = Math.abs(points[0].frequency - targetFreq);

        for (const point of points) {
            const distance = Math.abs(point.frequency - targetFreq);
            if (distance < minDistance) {
                minDistance = distance;
                closestPoint = point;
            }
        }

        // 如果找到完全匹配的频率点
        if (minDistance === 0) {
            return closestPoint.spl;
        }

        // 尝试线性插值
        let lowerPoint = null;
        let upperPoint = null;

        for (let i = 0; i < points.length - 1; i++) {
            if (points[i].frequency <= targetFreq && points[i + 1].frequency >= targetFreq) {
                lowerPoint = points[i];
                upperPoint = points[i + 1];
                break;
            }
        }

        // 如果可以进行线性插值
        if (lowerPoint && upperPoint && lowerPoint.frequency !== upperPoint.frequency) {
            const ratio = (targetFreq - lowerPoint.frequency) / (upperPoint.frequency - lowerPoint.frequency);
            return lowerPoint.spl + ratio * (upperPoint.spl - lowerPoint.spl);
        }

        // 否则返回最接近点的值
        return closestPoint.spl;
    }

    /**
     * 清理文件名，移除不合法的字符
     * @param {string} name 原始名称
     * @returns {string} 清理后的名称
     */
    sanitizeFileName(name) {
        return name
            .replace(/[<>:"/\\|?*]/g, '_')  // 替换不合法字符
            .replace(/\s+/g, '_')           // 替换空格
            .replace(/[()（）]/g, '')        // 移除括号
            .replace(/_+/g, '_')            // 合并多个下划线
            .replace(/^_|_$/g, '');         // 移除首尾下划线
    }

    /**
     * 处理单个目标曲线
     * @param {Object} targetData 目标曲线数据
     */
    async processTarget(targetData) {
        try {
            console.log(`正在处理目标曲线: ${targetData.name}`);
            
            // 解析数据
            const parsed = this.parseTargetData(targetData);
            
            if (parsed.dataPoints.length === 0) {
                console.log(`⚠️  目标曲线 ${targetData.name} 没有找到有效的数据点`);
                return;
            }
            
            console.log(`📊 解析到 ${parsed.dataPoints.length} 个数据点`);
            
            // 转换为标准频率点
            const standardPoints = this.interpolateToStandardFrequencies(parsed.dataPoints);
            
            // 清理文件名
            const sanitizedName = this.sanitizeFileName(targetData.name);
            
            // 创建输出数据结构（与频响文件格式一致）
            const outputData = {
                sourceName: 'target',
                brandName: 'ReaLab Target Curves',
                headphoneName: targetData.name,
                lastUpdated: new Date().toISOString(),
                frequencyData: {
                    [targetData.name]: {
                        title: `${targetData.name} - ReaLab Target`,
                        frequencies: this.STANDARD_FREQUENCIES,
                        spl_values: standardPoints.map(point => point.spl)
                    }
                }
            };
            
            // 保存文件
            const outputPath = path.join(this.targetDir, `${sanitizedName}.json`);
            
            await fs.writeJson(outputPath, outputData, { spaces: 2 });
            
            console.log(`✅ 成功保存: ${outputPath}`);
            console.log(`   - 频率范围: ${standardPoints[0].frequency}Hz - ${standardPoints[standardPoints.length - 1].frequency}Hz`);
            console.log(`   - SPL范围: ${Math.min(...standardPoints.map(p => p.spl)).toFixed(2)}dB - ${Math.max(...standardPoints.map(p => p.spl)).toFixed(2)}dB`);
            
        } catch (error) {
            console.error(`❌ 处理目标曲线 ${targetData.name} 失败:`, error.message);
        }
    }

    /**
     * 处理所有ReaLab目标曲线
     */
    async processAllTargets() {
        try {
            await fs.ensureDir(this.targetDir);
            
            console.log('🎯 开始处理ReaLab目标曲线...\n');
            
            // 获取目标曲线数据
            const targets = await this.fetchReaLabTargets();
            
            if (!targets || targets.length === 0) {
                console.log('⚠️  没有找到ReaLab目标曲线数据');
                return;
            }
            
            console.log(`📁 找到 ${targets.length} 个ReaLab目标曲线`);
            
            // 处理每个目标曲线
            for (const target of targets) {
                await this.processTarget(target);
                console.log(); // 空行分隔
            }
            
            console.log('🎉 所有ReaLab目标曲线处理完成！');
            console.log(`📂 输出目录: ${this.targetDir}`);
            
        } catch (error) {
            console.error('❌ 处理过程中发生错误:', error.message);
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const parser = new ReaLabTargetParser();
    parser.processAllTargets();
}

module.exports = ReaLabTargetParser;
