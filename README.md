# 耳机频响获取API

一个用于获取和缓存耳机频响数据的Node.js API服务，支持多个数据源（ReaLab、HuiHiFi）。

## 功能特性

- 🎧 支持多个数据源：ReaLab、HuiHiFi
- 📊 将原始数据转换为标准AutoEq频率点
- 💾 本地文件缓存系统（按数据源组织）
- ⏰ 每小时自动更新数据
- 🚀 RESTful API接口
- 📈 系统状态监控
- 🔄 并行数据获取和处理

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务

```bash
# 生产模式
npm start

# 开发模式（需要安装nodemon）
npm run dev
```

服务将在 `http://localhost:3000` 启动。

## API接口

### 1. 获取所有数据源

```http
GET /api/sources
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "name": "realab",
      "displayName": "ReaLab",
      "description": "ReaLab耳机频响数据源"
    },
    {
      "name": "huihifi",
      "displayName": "HuiHiFi",
      "description": "HuiHiFi耳机频响数据源"
    }
  ],
  "count": 2,
  "message": "获取数据源列表成功"
}
```

### 2. 获取指定数据源的所有品牌

```http
GET /api/sources/{sourceName}/brands
```

**响应示例：**
```json
{
  "success": true,
  "data": ["Apple_苹果", "Sony_索尼", "Sennheiser"],
  "count": 3,
  "sourceName": "realab",
  "message": "获取数据源realab的品牌列表成功"
}
```

### 3. 获取指定数据源和品牌下的所有耳机

```http
GET /api/sources/{sourceName}/brands/{brandName}/headphones
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "fileName": "Apple_苹果_AirPods_Max",
      "originalName": "Apple/苹果 AirPods Max",
      "lastUpdated": "2024-01-15T10:30:00.000Z",
      "sourceName": "realab"
    }
  ],
  "count": 1,
  "sourceName": "realab",
  "brandName": "Apple_苹果",
  "message": "获取数据源realab品牌Apple_苹果的耳机列表成功"
}
```

### 4. 获取指定数据源、品牌和耳机的频响数据

```http
GET /api/sources/{sourceName}/brands/{brandName}/headphones/{headphoneName}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "sourceName": "realab",
    "brandName": "Apple/苹果",
    "headphoneName": "Apple/苹果 AirPods Max",
    "lastUpdated": "2024-01-15T10:30:00.000Z",
    "frequencyData": {
      //注意：frequencyData下可能会有多个测量条件（以此示例来看，ANC on（B&K 5128）就是其中一个测量条件）
      "ANC on（B&K 5128）": {
        "title": "Apple/苹果 AirPods Max - ANC on（B&K 5128）",
        "frequencies": [20, 21, 22, ...],
        "spl_values": [97.852, 97.843, 97.833, ...]
      }
    }
  },
  "message": "获取耳机realab/Apple_苹果/Apple_苹果_AirPods_Max的频响数据成功"
}
```

### 5. 兼容性API（旧版本）

为了向后兼容，以下API仍然可用，但会搜索所有数据源：

```http
GET /api/brands                                    # 获取所有数据源的品牌
GET /api/brands/{brandName}/headphones             # 获取指定品牌在所有数据源中的耳机
GET /api/brands/{brandName}/headphones/{headphoneName}  # 获取指定耳机的频响数据
```

### 6. 系统状态

```http
GET /api/status
```

### 7. 健康检查

```http
GET /api/health
```

### 8. 手动触发更新

```http
POST /api/update
```

### 9. 获取所有目标曲线列表

```http
GET /api/targets
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "fileName": "Clouditer_Mimosa",
      "name": "Clouditer Mimosa",
      "lastUpdated": "2025-08-06T08:30:00.000Z",
      "measurementCount": 1
    },
    {
      "fileName": "Harman_Target",
      "name": "Harman Target",
      "lastUpdated": "2025-08-06T08:31:00.000Z",
      "measurementCount": 1
    }
  ],
  "count": 2,
  "message": "获取目标曲线列表成功"
}
```

### 10. 获取指定目标曲线数据

```http
GET /api/targets/{targetName}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "name": "Clouditer Mimosa",
    "lastUpdated": "2025-08-06T08:30:00.000Z",
    "frequencyData": {
      "EQ Clouditer Mimosa": {
        "title": "Clouditer_Mimosa - EQ Clouditer Mimosa",
        "frequencies": [20, 21, 22, ...],
        "spl_values": [95.409, 95.408, 95.407, ...]
      }
    }
  },
  "message": "获取目标曲线Clouditer_Mimosa成功"
}
```

### 11. 清理缓存

```http
DELETE /api/cache
```

## 数据结构

### 标准频率点

API使用标准AutoEq频率点（共120个频率点），从20Hz到19871Hz：

```javascript
[20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 34, 36, 38, 40, 43, 45, 48, 50, ...]
```

### 本地缓存结构

```
data/
├── metadata.json                           # 元数据信息
├── realab/                                 # ReaLab数据源目录
│   ├── Apple_苹果/                         # 品牌目录
│   │   ├── Apple_苹果_AirPods_Max.json     # 耳机数据文件
│   │   └── Apple_苹果_AirPods_4.json
│   └── Sony_索尼/
│       ├── Sony_索尼_WH-1000XM5.json
│       └── ...
├── huihifi/                                # HuiHiFi数据源目录
│   ├── 水月雨_MoonDrop/                    # 品牌目录
│   │   ├── 水月雨_乐园2_网友送测.json
│   │   └── 水月雨_光束Rays.json
│   └── ...
└── target/                                 # 目标曲线目录（独立于数据源）
    ├── Clouditer_Mimosa.json              # 目标曲线文件
    ├── Harman_Target.json
    └── ...
```

**注意**：`target/` 目录专门用于存储目标曲线，不会被计入频响数据源统计中。

## 数据源 vs 目标曲线

### 数据源（Sources）
- **用途**：存储实际耳机的频响测量数据
- **来源**：ReaLab、HuiHiFi等测评网站
- **API**：`/api/sources/*` 系列接口
- **特点**：按品牌和型号组织，包含多种测量条件

### 目标曲线（Targets）
- **用途**：存储理想的频响目标曲线
- **来源**：REW导出的目标曲线文件
- **API**：`/api/targets/*` 专用接口
- **特点**：独立管理，用于对比分析

### 区别说明
| 项目 | 数据源 | 目标曲线 |
|------|--------|----------|
| 数据类型 | 实际测量 | 理想目标 |
| 组织方式 | 品牌/型号 | 直接列表 |
| 统计计入 | ✅ 是 | ❌ 否 |
| 更新方式 | 自动爬取 | 手动解析 |

## 配置选项

### 环境变量

- `PORT`: 服务端口（默认：3000）
- `NODE_ENV`: 运行环境（development/production）
- `CORS_ORIGIN`: CORS允许的源（默认：*）

### 定时任务

- 自动更新频率：每小时（可在 `src/services/dataSync.js` 中修改）
- 请求间隔：品牌间1秒，产品间200毫秒

## 项目结构

```
src/
├── app.js                    # 主应用文件
├── routes/
│   └── api.js               # API路由
└── services/
    ├── realabService.js     # ReaLab数据获取服务
    ├── frequencyProcessor.js # 频响数据处理服务
    ├── cacheManager.js      # 缓存管理服务
    └── dataSync.js          # 数据同步服务
```

## 开发说明

### 添加新的数据源

1. 在 `src/services/` 目录下创建新的服务类
2. 实现数据获取接口
3. 在 `dataSync.js` 中集成新的数据源

### 修改频率点

在 `src/services/frequencyProcessor.js` 中修改 `STANDARD_FREQUENCIES` 数组。

### 自定义缓存策略

在 `src/services/cacheManager.js` 中修改缓存逻辑。

## 注意事项

- 首次启动时会自动获取所有数据，可能需要较长时间
- 请求频率已经过优化，避免对源网站造成过大压力
- 建议在生产环境中使用进程管理器（如PM2）
- 定期备份 `data/` 目录中的缓存数据

## 许可证

MIT License
